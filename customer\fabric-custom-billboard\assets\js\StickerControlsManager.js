/**
 * StickerControlsManager - Handles sticker delete controls and color customization
 * Mobile-first responsive sticker controls for billboard applications
 */
class StickerControlsManager {
    constructor(canvasManager, options = {}) {
        this.canvasManager = canvasManager;
        this.canvas = canvasManager.getCanvas();
        this.options = {
            deleteControlSize: 20,
            colorOptions: [
                '#000000', '#ffffff', '#ff0000', '#00ff00', '#0000ff',
                '#ffff00', '#ff00ff', '#00ffff', '#ffa500', '#800080',
                '#008000', '#800000', '#000080', '#808080', '#c0c0c0'
            ],
            ...options
        };
        
        // State management
        this.selectedSticker = null;
        this.stickerColorPicker = null;
        
        this.init();
    }
    
    /**
     * Initialize sticker controls manager
     */
    init() {
        this.setupDeleteControls();
        this.setupSelectionHandling();
        this.createColorPickerUI();
        console.log('✅ StickerControlsManager initialized');
    }
    
    /**
     * Setup delete controls for stickers
     */
    setupDeleteControls() {
        // Add delete control to stickers when they're added to canvas
        this.canvas.on('object:added', (e) => {
            const obj = e.target;
            if (obj && obj.stickerData) {
                this.addDeleteControl(obj);
            }
        });
    }
    
    /**
     * Add delete control to a sticker object
     */
    addDeleteControl(stickerObj) {
        stickerObj.controls.deleteControl = new fabric.Control({
            x: 0.5,
            y: -0.5,
            offsetY: -8,
            offsetX: 8,
            cursorStyle: 'pointer',
            mouseUpHandler: (eventData, transform) => {
                const target = transform.target;
                this.canvas.remove(target);
                this.canvas.renderAll();
                
                // Hide sticker properties if this sticker was selected
                if (this.selectedSticker === target) {
                    this.hideStickerProperties();
                    this.selectedSticker = null;
                }
                
                console.log('✅ Sticker deleted with X button');
                this.emit('sticker:deleted', { sticker: target });
            },
            render: (ctx, left, top, styleOverride, fabricObject) => {
                const size = this.options.deleteControlSize;
                ctx.save();
                ctx.translate(left, top);
                ctx.rotate(fabric.util.degreesToRadians(fabricObject.angle));
                
                // Draw red circle background
                ctx.fillStyle = '#ff0000';
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.arc(0, 0, size/2, 0, 2 * Math.PI);
                ctx.fill();
                ctx.stroke();
                
                // Draw white X
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = 3;
                ctx.beginPath();
                ctx.moveTo(-6, -6);
                ctx.lineTo(6, 6);
                ctx.moveTo(6, -6);
                ctx.lineTo(-6, 6);
                ctx.stroke();
                
                ctx.restore();
            },
            cornerSize: this.options.deleteControlSize
        });
    }
    
    /**
     * Setup selection handling for stickers
     */
    setupSelectionHandling() {
        this.canvas.on('selection:created', (e) => {
            this.handleStickerSelection(e.selected);
        });
        
        this.canvas.on('selection:updated', (e) => {
            this.handleStickerSelection(e.selected);
        });
        
        this.canvas.on('selection:cleared', () => {
            this.handleStickerDeselection();
        });
    }
    
    /**
     * Handle sticker selection
     */
    handleStickerSelection(selectedObjects) {
        const stickers = selectedObjects.filter(obj => obj.stickerData);
        
        if (stickers.length === 1) {
            this.selectedSticker = stickers[0];
            this.showStickerProperties();
            this.updateColorPicker();
        } else {
            this.selectedSticker = null;
            this.hideStickerProperties();
        }
    }
    
    /**
     * Handle sticker deselection
     */
    handleStickerDeselection() {
        this.selectedSticker = null;
        this.hideStickerProperties();
    }
    
    /**
     * Create color picker UI for stickers
     */
    createColorPickerUI() {
        this.stickerColorPicker = document.createElement('div');
        this.stickerColorPicker.className = 'sticker-color-picker';
        this.stickerColorPicker.innerHTML = this.generateColorPickerHTML();
        
        this.setupColorPickerEvents();
    }
    
    /**
     * Generate color picker HTML
     */
    generateColorPickerHTML() {
        return `
            <div class="sticker-color-header">
                <i class="fas fa-palette"></i>
                <span>Sticker Color</span>
            </div>
            <div class="sticker-color-grid">
                ${this.options.colorOptions.map(color => `
                    <div class="sticker-color-option" 
                         data-color="${color}" 
                         style="background-color: ${color}"
                         title="${color.toUpperCase()}"
                         role="button"
                         tabindex="0"
                         aria-label="Select color ${color}">
                    </div>
                `).join('')}
            </div>
            <div class="sticker-custom-color">
                <label for="stickerCustomColor">Custom:</label>
                <input type="color" id="stickerCustomColor" class="sticker-custom-color-input" value="#000000">
            </div>
        `;
    }
    
    /**
     * Setup color picker event listeners
     */
    setupColorPickerEvents() {
        // Color option clicks
        this.stickerColorPicker.addEventListener('click', (e) => {
            if (e.target.classList.contains('sticker-color-option')) {
                const color = e.target.dataset.color;
                this.applyStickerColor(color);
                this.updateColorSelection(color);
            }
        });
        
        // Custom color input
        const customColorInput = this.stickerColorPicker.querySelector('#stickerCustomColor');
        if (customColorInput) {
            customColorInput.addEventListener('change', (e) => {
                this.applyStickerColor(e.target.value);
                this.updateColorSelection(e.target.value);
            });
        }
        
        // Keyboard navigation
        this.stickerColorPicker.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                if (e.target.classList.contains('sticker-color-option')) {
                    e.preventDefault();
                    const color = e.target.dataset.color;
                    this.applyStickerColor(color);
                    this.updateColorSelection(color);
                }
            }
        });
    }
    
    /**
     * Apply color to selected sticker
     */
    applyStickerColor(color) {
        if (!this.selectedSticker || !this.selectedSticker.stickerData) return;
        
        // For SVG-based stickers, we need to update the fill color
        this.selectedSticker.set('fill', color);
        this.canvas.renderAll();
        
        console.log('✅ Sticker color applied:', color);
        this.emit('sticker:color:changed', { 
            sticker: this.selectedSticker, 
            color: color 
        });
    }
    
    /**
     * Update color selection visual feedback
     */
    updateColorSelection(selectedColor) {
        const colorOptions = this.stickerColorPicker.querySelectorAll('.sticker-color-option');
        colorOptions.forEach(option => {
            option.classList.remove('selected');
            if (option.dataset.color === selectedColor) {
                option.classList.add('selected');
            }
        });
        
        // Update custom color input
        const customColorInput = this.stickerColorPicker.querySelector('#stickerCustomColor');
        if (customColorInput) {
            customColorInput.value = selectedColor;
        }
    }
    
    /**
     * Update color picker based on selected sticker
     */
    updateColorPicker() {
        if (!this.selectedSticker) return;
        
        const currentColor = this.selectedSticker.fill || '#000000';
        this.updateColorSelection(currentColor);
    }
    
    /**
     * Show sticker properties panel
     */
    showStickerProperties() {
        const stickerProperties = document.getElementById('stickerProperties');
        if (stickerProperties) {
            stickerProperties.style.display = 'block';
        }
        
        this.emit('sticker:properties:shown');
    }
    
    /**
     * Hide sticker properties panel
     */
    hideStickerProperties() {
        const stickerProperties = document.getElementById('stickerProperties');
        if (stickerProperties) {
            stickerProperties.style.display = 'none';
        }
        
        this.emit('sticker:properties:hidden');
    }
    
    /**
     * Get color picker element
     */
    getColorPickerElement() {
        return this.stickerColorPicker;
    }
    
    /**
     * Emit custom events
     */
    emit(eventName, data = {}) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
    }
    
    /**
     * Cleanup
     */
    destroy() {
        if (this.stickerColorPicker && this.stickerColorPicker.parentNode) {
            this.stickerColorPicker.parentNode.removeChild(this.stickerColorPicker);
        }
        this.selectedSticker = null;
    }
}

// Export for global use
window.StickerControlsManager = StickerControlsManager;
